<!DOCTYPE html><html lang="zh-CN"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亚马逊图片批量上传辅助工具</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/fonts/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }

        .header {
            background: linear-gradient(135deg, #ff7b7b 0%, #667eea 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 20px;
        }

        .file-drop-zone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.1);
        }

        .file-drop-zone.dragover {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .file-drop-zone.dragover h5 {
            color: #28a745 !important;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .progress {
            height: 20px;
            border-radius: 10px;
        }

        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .result-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .result-item.error {
            border-left-color: #dc3545;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            font-weight: 600;
            margin-right: 5px;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 15px 15px 15px;
            padding: 30px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .status-processing {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .history-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
        }

        .table tbody td {
            padding: 15px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .url-input-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }

        .method-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 15px !important;
        }

        .method-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
        }

        .method-card .card-header {
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }

        .method-card .card-body {
            padding: 25px;
        }

        .method-card .card-footer {
            padding: 20px;
            border-radius: 0 0 15px 15px !important;
        }

        .method-card .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .method-card .btn {
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .method-card .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
                border-radius: 15px 15px 0 0;
            }

            .feature-card {
                margin: 10px 0;
                padding: 20px;
            }

            .method-card .card-body {
                padding: 20px;
            }

            .method-card .card-header {
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-cloud-upload"></i> 亚马逊图片批量上传辅助工具</h1>
                <p class="mb-0">支持两种上传方式：压缩文件上传 &amp; 模板上传，满足不同场景需求</p>
            </div>

            <!-- 主要内容 -->
            <div class="p-4">
                <!-- 方法选择说明 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info border-0" style="background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);">
                            <h5 class="alert-heading"><i class="bi bi-lightbulb"></i> 选择适合您的上传方式</h5>
                            <p class="mb-0">根据您的图片状态和需求，选择最适合的上传方式：</p>
                        </div>
                    </div>
                </div>

                <!-- 两种方法的卡片展示 -->
                <div class="row g-4 mb-4">
                    <!-- 方法1：压缩文件上传 -->
                    <div class="col-lg-6">
                        <div class="card h-100 border-0 shadow-sm method-card" style="background: linear-gradient(135deg, #fff3e0 0%, #ffecb3 100%);">
                            <div class="card-header border-0 text-center" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white;">
                                <h4 class="mb-0"><i class="bi bi-archive"></i> 方法1：压缩文件上传</h4>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);">
                                        <i class="bi bi-tools"></i>
                                    </div>
                                    <h5 class="mt-3">🔧 图片重命名工具</h5>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-success"><i class="bi bi-check-circle"></i> 适用场景：</h6>
                                    <ul class="list-unstyled ms-3">
                                        <li><i class="bi bi-dot"></i> 图片文件名不规范，需要批量重命名</li>
                                        <li><i class="bi bi-dot"></i> 希望生成压缩包直接上传到亚马逊</li>
                                        <li><i class="bi bi-dot"></i> 不需要使用图床服务</li>
                                        <li><i class="bi bi-dot"></i> 图片数量较少，一次性处理</li>
                                    </ul>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-primary"><i class="bi bi-gear"></i> 主要功能：</h6>
                                    <ul class="list-unstyled ms-3">
                                        <li><i class="bi bi-arrow-right text-primary"></i> 批量重命名图片文件</li>
                                        <li><i class="bi bi-arrow-right text-primary"></i> 自动生成压缩包</li>
                                        <li><i class="bi bi-arrow-right text-primary"></i> 支持主图、场景图、色块图</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="card-footer border-0 text-center" style="background: transparent;">
                                <button class="btn btn-warning btn-lg px-4" onclick="showRenameTools()">
                                    <i class="bi bi-play-circle"></i> 开始使用
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 方法2：模板上传 -->
                    <div class="col-lg-6">
                        <div class="card h-100 border-0 shadow-sm method-card" style="background: linear-gradient(135deg, #f3e5f5 0%, #fce4ec 100%);">
                            <div class="card-header border-0 text-center" style="background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%); color: white;">
                                <h4 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> 方法2：模板上传</h4>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); width: 50px; height: 50px; font-size: 20px;">
                                                <i class="bi bi-link-45deg"></i>
                                            </div>
                                            <h6 class="mt-2">图片链接管理</h6>
                                        </div>
                                        <div class="col-6">
                                            <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); width: 50px; height: 50px; font-size: 20px;">
                                                <i class="bi bi-file-text"></i>
                                            </div>
                                            <h6 class="mt-2">模板填充</h6>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-success"><i class="bi bi-check-circle"></i> 适用场景：</h6>
                                    <ul class="list-unstyled ms-3">
                                        <li><i class="bi bi-dot"></i> 图片需要上传到图床获取链接</li>
                                        <li><i class="bi bi-dot"></i> 需要填充亚马逊模板文件</li>
                                        <li><i class="bi bi-dot"></i> 批量处理商品信息和图片映射</li>
                                        <li><i class="bi bi-dot"></i> 图片数量较多，需要在线管理</li>
                                    </ul>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-primary"><i class="bi bi-gear"></i> 主要功能：</h6>
                                    <ul class="list-unstyled ms-3">
                                        <li><i class="bi bi-arrow-right text-primary"></i> 批量上传图片到图床</li>
                                        <li><i class="bi bi-arrow-right text-primary"></i> 生成图片链接映射表</li>
                                        <li><i class="bi bi-arrow-right text-primary"></i> 自动填充亚马逊模板</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="card-footer border-0 text-center" style="background: transparent;">
                                <button class="btn btn-primary btn-lg px-4" onclick="showTemplateTools()">
                                    <i class="bi bi-play-circle"></i> 开始使用
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工具内容区域 -->
                <div id="toolsContainer" style="display: none;">
                    <!-- 返回选择按钮 -->
                    <div class="mb-3 text-center">
                        <button class="btn btn-outline-secondary btn-sm" onclick="backToMethodSelection()">
                            <i class="bi bi-arrow-left"></i> 返回方法选择
                        </button>
                    </div>

                    <!-- 导航标签 -->
                    <ul class="nav nav-tabs mb-4" id="mainTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="folder-tab" data-bs-toggle="tab" data-bs-target="#folder-tab-pane" type="button" role="tab" aria-controls="folder-tab-pane" aria-selected="true">
                                <i class="bi bi-folder-plus"></i> 文件夹管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="rename-tab" data-bs-toggle="tab" data-bs-target="#rename-tab-pane" type="button" role="tab" aria-controls="rename-tab-pane" aria-selected="false">
                                <i class="bi bi-tools"></i> 图片重命名工具
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-tab-pane" type="button" role="tab" aria-controls="upload-tab-pane" aria-selected="false">
                                <i class="bi bi-link-45deg"></i> 图片链接管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="template-tab" data-bs-toggle="tab" data-bs-target="#template-tab-pane" type="button" role="tab" aria-controls="template-tab-pane" aria-selected="false">
                                <i class="bi bi-file-earmark-spreadsheet"></i> 模板填充
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history-tab-pane" type="button" role="tab" aria-controls="history-tab-pane" aria-selected="false">
                                <i class="bi bi-clock-history"></i> 历史记录
                            </button>
                        </li>
                    </ul>

                <div class="tab-content" id="mainTabContent">
                    <!-- 文件夹管理选项卡 -->
                    <div class="tab-pane fade show active" id="folder-tab-pane" role="tabpanel" aria-labelledby="folder-tab" tabindex="0">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="feature-card">
                                    <div class="text-center mb-4">
                                        <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);">
                                            <i class="bi bi-folder-plus"></i>
                                        </div>
                                        <h4 class="mt-3">📁 文件夹管理</h4>
                                        <p class="text-muted">根据SKU列表批量导出文件夹，为图片处理做准备</p>
                                    </div>

                                    <div class="alert alert-info border-0" style="background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);">
                                        <h6><i class="bi bi-info-circle"></i> 功能说明</h6>
                                        <p class="mb-2"><strong>🎯 目标：根据SKU列表导出对应的图片文件夹</strong></p>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 读取Excel中的SKU列表</p>
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 递归搜索对应文件夹</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 批量复制到目标位置</p>
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 可直接用于后续图片处理</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 文件上传区域 -->
                                    <div class="mb-4">
                                        <label class="form-label">
                                            <i class="bi bi-file-earmark-excel text-success"></i>
                                            SKU列表Excel文件 <span class="text-danger">*</span>
                                        </label>
                                        <div class="upload-area" id="folderExcelUploadArea">
                                            <input type="file" id="folderExcelFile" accept=".xlsx,.xls,.xlsm" style="display: none;">
                                            <div class="upload-placeholder">
                                                <i class="bi bi-file-earmark-excel upload-icon"></i>
                                                <p class="mb-2">点击选择或拖拽Excel文件</p>
                                                <small class="text-muted">支持 .xlsx, .xls, .xlsm 格式，需包含SKU列</small>
                                            </div>
                                            <div class="upload-success" style="display: none;">
                                                <i class="bi bi-check-circle text-success"></i>
                                                <span class="filename"></span>
                                                <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="clearFolderExcelFile()">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 路径选择区域 -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <label class="form-label">
                                                <i class="bi bi-folder text-primary"></i>
                                                源文件夹路径 <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="folderSourcePath" placeholder="包含SKU文件夹的根目录">
                                                <button class="btn btn-outline-secondary" type="button" onclick="selectFolderSourcePath()">
                                                    <i class="bi bi-folder"></i> 选择
                                                </button>
                                            </div>
                                            <small class="text-muted">将在此目录及其子目录中搜索SKU文件夹</small>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">
                                                <i class="bi bi-folder-plus text-warning"></i>
                                                导出目标路径 <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="folderExportPath" placeholder="导出文件夹的存放位置">
                                                <button class="btn btn-outline-secondary" type="button" onclick="selectFolderExportPath()">
                                                    <i class="bi bi-folder-plus"></i> 选择
                                                </button>
                                            </div>
                                            <small class="text-muted">将创建带时间戳的子文件夹存放导出结果</small>
                                        </div>
                                    </div>

                                    <!-- 导出选项 -->
                                    <div class="mb-4">
                                        <label class="form-label">
                                            <i class="bi bi-gear text-secondary"></i>
                                            导出选项
                                        </label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="createTimestampFolder" checked="">
                                                    <label class="form-check-label" for="createTimestampFolder">
                                                        创建时间戳子文件夹
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="continueToImageProcessing" checked="">
                                                    <label class="form-check-label" for="continueToImageProcessing">
                                                        导出后继续图片处理流程
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="text-center">
                                        <button class="btn btn-success btn-lg px-4" id="startFolderExport" onclick="startFolderExport()">
                                            <i class="bi bi-play-circle"></i> 开始导出
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 进度显示区域 -->
                                <div class="feature-card" id="folderProgressCard" style="display: none;">
                                    <h5><i class="bi bi-activity"></i> 导出进度</h5>
                                    <div class="progress mb-3">
                                        <div class="progress-bar" id="folderProgressBar" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                    <div id="folderProgressInfo">
                                        <p class="mb-1"><strong>当前状态：</strong><span id="folderCurrentStatus">准备中...</span></p>
                                        <p class="mb-1"><strong>处理进度：</strong><span id="folderProgressText">0/0</span></p>
                                        <p class="mb-1"><strong>成功：</strong><span id="folderSuccessCount">0</span></p>
                                        <p class="mb-1"><strong>失败：</strong><span id="folderFailedCount">0</span></p>
                                    </div>
                                </div>

                                <!-- 结果显示区域 -->
                                <div class="feature-card" id="folderResultCard" style="display: none;">
                                    <h5><i class="bi bi-check-circle"></i> 导出结果</h5>
                                    <div id="folderResults"></div>
                                    <div class="mt-3" id="folderActionSection" style="display: none;">
                                        <button class="btn btn-primary w-100 mb-2" id="continueImageProcessing">
                                            <i class="bi bi-arrow-right"></i> 继续图片处理
                                        </button>
                                        <button class="btn btn-outline-secondary w-100" id="openExportFolder">
                                            <i class="bi bi-folder"></i> 打开导出文件夹
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图片重命名选项卡 -->
                    <div class="tab-pane fade" id="rename-tab-pane" role="tabpanel" aria-labelledby="rename-tab" tabindex="0">
                        <div class="row">
                            <div class="col-12">
                                <div class="feature-card">
                                    <div class="text-center mb-4">
                                        <div class="feature-icon">
                                            <i class="bi bi-magic"></i>
                                        </div>
                                        <h4>🔧 图片重命名工具</h4>
                                        <p class="text-muted">简洁高效的图片重命名解决方案，支持主图、场景图、色块图批量处理</p>
                                    </div>

                                    <form id="renameForm" enctype="multipart/form-data">
                                        <!-- 文件选择区域 -->
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <div class="card border-success">
                                                    <div class="card-header bg-success text-white">
                                                        <h6 class="mb-0">
                                                            <i class="bi bi-file-earmark-excel"></i>
                                                            第一步：选择Excel映射文件
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <input type="file" class="form-control" id="rename_excel_file" accept=".xlsx,.xls,.xlsm" required="">

                                                        <!-- 文件名显示区域 -->
                                                        <div class="mt-2" id="rename_excel_filename_display" style="display: none;">
                                                            <div class="alert alert-success py-2 mb-2">
                                                                <i class="bi bi-check-circle-fill me-2"></i>
                                                                <strong>已选择：</strong>
                                                                <span id="rename_excel_filename" class="text-break"></span>
                                                                <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="clearRenameExcelFile()">
                                                                    <i class="bi bi-x"></i>
                                                                </button>
                                                            </div>
                                                        </div>

                                                        <div class="form-text mt-2">
                                                            <i class="bi bi-info-circle text-primary"></i>
                                                            必须包含：<strong>SKU</strong>、<strong>ASIN</strong>列<br>
                                                            可选包含：<strong>Color</strong>列（用于色块图处理）
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="card border-primary">
                                                    <div class="card-header bg-primary text-white">
                                                        <h6 class="mb-0">
                                                            <i class="bi bi-folder2-open"></i>
                                                            第二步：选择图片文件夹
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <input type="file" class="form-control" id="rename_image_folder" webkitdirectory="" multiple="" required="">
                                                        <div class="form-text mt-2">
                                                            <i class="bi bi-info-circle text-primary"></i>
                                                            支持多级目录结构，自动识别：<br>
                                                            📁 白底图、场景图、色块 等文件夹
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 处理选项 -->
                                        <div class="mb-4">
                                            <label class="form-label">
                                                <i class="bi bi-gear-fill text-primary"></i>
                                                处理选项
                                            </label>

                                            <!-- 主要选项 -->
                                            <div class="row g-3">
                                                <div class="col-md-4">
                                                    <div class="card h-100 border-primary">
                                                        <div class="card-body p-3">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" name="process_main" id="process_main">
                                                                <label class="form-check-label fw-bold" for="process_main">
                                                                    🖼️ 主图处理
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">处理白底图，重命名为 ASIN_MAIN.jpg</small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="card h-100 border-success">
                                                        <div class="card-body p-3">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" name="process_scene" id="process_scene">
                                                                <label class="form-check-label fw-bold" for="process_scene">
                                                                    🎬 场景图处理
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">处理场景图，重命名为 ASIN_PTXX.jpg</small>
                                                            <div class="form-check mt-2">
                                                                <input class="form-check-input form-check-input-sm" type="checkbox" name="scene_generic" id="scene_generic">
                                                                <label class="form-check-label small" for="scene_generic">
                                                                    📏 按宽度分类输出
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="card h-100 border-warning">
                                                        <div class="card-body p-3">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" name="process_swatch" id="process_swatch">
                                                                <label class="form-check-label fw-bold" for="process_swatch">
                                                                    🎨 色块图处理
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">处理色块图，重命名为 ASIN_SWCH.jpg</small>
                                                            <div class="mt-1">
                                                                <small class="text-info">
                                                                    <i class="bi bi-info-circle"></i>
                                                                    需要Excel包含Color列
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 操作按钮 -->
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="d-grid gap-2 d-md-flex">
                                                    <button type="button" class="btn btn-primary btn-lg flex-fill" id="renameSubmitBtn" onclick="handleRenameSubmit(event);">
                                                        <i class="bi bi-magic me-2"></i>开始处理
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" id="testColorMatchBtn" onclick="handleColorMatchTest(event);" data-bs-toggle="tooltip" data-bs-placement="top" title="测试Excel颜色与图片文件的匹配关系">
                                                        <i class="bi bi-palette me-2"></i>测试匹配
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <!-- 快速提示 -->
                                                <div class="alert alert-light border-0 p-2 mb-0">
                                                    <small class="text-muted">
                                                        <i class="bi bi-lightbulb text-warning"></i>
                                                        <strong>提示：</strong>处理色块图需要Excel包含Color列，文件夹名包含"色块"字样
                                                    </small>
                                                </div>
                                            </div>
                                        </div>





                                        <!-- 状态显示区域 -->
                                        <div class="mt-4">
                                            <!-- 处理进度 -->
                                            <div id="processing-status" class="alert alert-info" style="display: none;">
                                                <div class="d-flex align-items-center">
                                                    <div class="spinner-border spinner-border-sm me-3" role="status">
                                                        <span class="visually-hidden">处理中...</span>
                                                    </div>
                                                    <div>
                                                        <strong>正在处理中...</strong>
                                                        <div id="processing-message" class="small text-muted">请稍候，正在重命名图片文件</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 成功提示 -->
                                            <div id="success-message" class="alert alert-success" style="display: none;">
                                                <h6><i class="bi bi-check-circle"></i> 处理完成</h6>
                                                <div id="success-details"></div>
                                            </div>

                                            <!-- 错误提示 -->
                                            <div id="rename-error" class="alert alert-danger" style="display: none;">
                                                <h6><i class="bi bi-exclamation-triangle"></i> 处理失败</h6>
                                                <div id="error-details"></div>
                                            </div>

                                            <!-- 测试结果 -->
                                            <div id="rename-results-section" class="card" style="display: none;">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">
                                                        <i class="bi bi-clipboard-data text-primary"></i>
                                                        颜色匹配测试结果
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div id="color-match-results">
                                                        <!-- 结果内容将通过JavaScript填充 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图片链接管理选项卡 -->
                    <div class="tab-pane fade" id="upload-tab-pane" role="tabpanel" aria-labelledby="upload-tab" tabindex="0">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-images"></i>
                                    </div>
                                    <div class="text-center mb-4">
                                        <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);">
                                            <i class="bi bi-link-45deg"></i>
                                        </div>
                                        <h4 class="mt-3">🔗 图片链接管理</h4>
                                        <p class="text-muted">方法2的第一步：将图片上传到图床，生成链接映射表</p>
                                    </div>

                                    <div class="alert alert-success border-0" style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);">
                                        <h6><i class="bi bi-target"></i> 功能说明</h6>
                                        <p class="mb-2"><strong>🎯 目标：生成图片URL映射表，为模板填充做准备</strong></p>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 上传图片到图床获取链接</p>
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 自动识别ASIN和图片类型</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 生成Excel映射表</p>
                                                <p class="mb-1"><i class="bi bi-arrow-right text-success"></i> 供模板填充功能使用</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 功能选择 -->
                                    <div class="alert alert-primary">
                                        <h6><i class="bi bi-gear"></i> 选择处理方式</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="linkMethod" id="uploadMethod" value="upload" checked="">
                                                    <label class="form-check-label" for="uploadMethod">
                                                        <strong><i class="bi bi-cloud-upload"></i> 上传图片</strong><br>
                                                        <small class="text-muted">上传本地图片到图床</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="linkMethod" id="parseMethod" value="parse">
                                                    <label class="form-check-label" for="parseMethod">
                                                        <strong><i class="bi bi-link-45deg"></i> 解析URL</strong><br>
                                                        <small class="text-muted">处理已有的图片链接</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 上传图片区域 -->
                                    <div id="uploadSection">
                                        <div class="file-drop-zone" id="fileDropZone">
                                            <i class="bi bi-folder2-open" style="font-size: 48px; color: #667eea; margin-bottom: 20px;"></i>
                                            <h5><i class="bi bi-1-circle-fill text-primary"></i> 第一步：拖拽图片文件夹到此处或点击选择图片</h5>
                                            <p class="text-muted">支持 PNG, JPG, JPEG, GIF, BMP, WEBP 格式</p>
                                            <div class="alert alert-success py-2 mb-2">
                                                <strong>📁 拖拽支持:</strong><br>
                                                • 支持直接拖拽整个文件夹（包含子文件夹）<br>
                                                • 自动递归扫描所有图片文件<br>
                                                • 与桌面版本操作体验一致
                                            </div>
                                            <div class="alert alert-info py-2 mb-2">
                                                <strong>📝 文件命名要求:</strong><br>
                                                • 格式: <code>B0XXXXXXXXX_类型.扩展名</code><br>
                                                • 类型: <code>MAIN</code>(主图) / <code>PT01-PT08</code>(附图) / <code>SWCH</code>(色块图)<br>
                                                • 示例: <code>B0ABCDEFGH_MAIN.jpg</code>, <code>B0ABCDEFGH_PT01.jpg</code>, <code>B0ABCDEFGH_SWCH.jpg</code>
                                            </div>
                                            <p class="text-warning"><small><i class="bi bi-exclamation-triangle"></i>
                                                    建议选择包含所有图片的文件夹，与桌面版本操作一致</small></p>
                                            <input type="file" id="fileInput" multiple="" accept="image/*" webkitdirectory="" style="display: none;">
                                            <div class="mt-3">
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="selectFilesBtn">
                                                    <i class="bi bi-images"></i> 选择图片文件
                                                </button>
                                                <button type="button" class="btn btn-primary btn-sm" id="selectFolderBtn">
                                                    <i class="bi bi-folder2-open"></i> 选择图片文件夹 (推荐)
                                                </button>
                                            </div>
                                            <input type="file" id="filesInput" multiple="" accept="image/*" style="display: none;">
                                        </div>
                                    </div>

                                    <!-- URL解析区域 -->
                                    <div id="parseSection" style="display: none;">
                                        <div class="alert alert-info">
                                            <h6><i class="bi bi-info-circle"></i> 使用说明</h6>
                                            <p class="mb-2">每行粘贴一个图片URL，系统自动提取ASIN和图片类型</p>
                                            <p class="mb-0 small">支持格式：直接URL 或 文件名|URL</p>
                                        </div>
                                        <textarea id="urlInput" class="form-control" rows="8" placeholder="https://example.com/image/B07XXXXX_MAIN.jpg
https://example.com/image/B07XXXXX_PT01.jpg

或者：
B07XXXXX_MAIN.jpg|https://example.com/image/B07XXXXX_MAIN.jpg"></textarea>
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <button class="btn btn-primary w-100" id="parseUrlBtn">
                                                    <i class="bi bi-file-earmark-spreadsheet"></i> 解析URL数据
                                                </button>
                                            </div>
                                            <div class="col-md-6">
                                                <button class="btn btn-outline-secondary w-100" id="clearUrlBtn">
                                                    <i class="bi bi-trash"></i> 清空
                                                </button>
                                            </div>
                                        </div>

                                        <!-- URL解析结果 -->
                                        <div id="urlParseSteps" style="display: none;">
                                            <div class="alert alert-success mt-3" id="urlParseSuccess">
                                                <h6><i class="bi bi-check-circle"></i> URL解析完成</h6>
                                                <p class="mb-0">
                                                    成功解析 <span id="parsedUrlCount">0</span> 个URL，涉及 <span id="parsedAsinCount">0</span> 个ASIN
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 上传设置（仅上传模式显示） -->
                                    <div class="mt-4" id="uploadSettings" style="display: none;">
                                        <div class="alert alert-warning">
                                            <h6><i class="bi bi-folder-plus"></i> 第二步：设置上传目录</h6>
                                            <p class="mb-3">
                                                <strong>请输入要在图床创建的一级目录名</strong><br>
                                                <small class="text-muted">与桌面版本操作完全一致，这是必须的步骤</small>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <input type="text" class="form-control" id="uploadFolder" placeholder="请输入目录名（不能包含/或\，不能为空）">
                                                    <small class="text-muted">
                                                        <i class="bi bi-lightbulb"></i>
                                                        示例：产品图片、新品上传、DEFAULT、测试图片 等
                                                    </small>
                                                </div>
                                                <div class="col-md-4">
                                                    <button class="btn btn-success w-100" id="confirmFolderBtn" disabled="">
                                                        <i class="bi bi-arrow-right"></i> 下一步
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 任务列表文件选择 -->
                                    <div class="mt-4" id="taskListSection" style="display: none;">
                                        <div class="alert alert-info">
                                            <h6><i class="bi bi-file-earmark-excel"></i> <span id="taskListStepTitle">第三步：选择商品信息文件 (可选)</span></h6>
                                            <p class="mb-3">
                                                <strong>选择包含ASIN、SKU、MSKU信息的Excel文件</strong><br>
                                                <small class="text-muted">
                                                    用于自动填充映射表中的商品信息
                                                </small>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <input type="file" class="form-control" id="taskListFile" accept=".xlsx,.xls,.xlsm">
                                                    <small class="text-muted mt-1 d-block">
                                                        <i class="bi bi-info-circle"></i>
                                                        Excel文件应包含ASIN、SKU、MSKU等列
                                                    </small>
                                                </div>
                                                <div class="col-md-4">
                                                    <button class="btn btn-success w-100" id="startProcessBtn">
                                                        <i class="bi bi-play-circle"></i> <span id="processButtonText">开始上传</span>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="alert alert-light mt-3 mb-0">
                                                <small class="text-muted">
                                                    <i class="bi bi-lightbulb"></i>
                                                    <strong>提示：</strong><span id="processHintText">不选择文件将生成基础映射表</span>
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 选中的文件列表 -->
                                    <div id="selectedFiles" class="mt-4" style="display: none;">
                                        <h6>已选择的文件:</h6>
                                        <div id="fileList" class="list-group"></div>
                                    </div>

                                    <!-- 处理结果显示 -->
                                    <div id="resultCard" class="feature-card mt-3" style="display: none;">
                                        <h5><i class="bi bi-check-circle"></i> <span id="resultTitle">处理完成</span></h5>
                                        <div id="resultSummary">
                                            <p class="mb-2">
                                                <i class="bi bi-bar-chart"></i> 统计信息：
                                                共处理 <span id="resultAsinCount">0</span> 个ASIN，
                                                <span id="resultTotalCount">0</span> 个链接
                                            </p>
                                        </div>
                                        <div class="mt-3">
                                            <button class="btn btn-success w-100" id="downloadResultExcel">
                                                <i class="bi bi-download"></i> 下载映射表
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 上传进度 -->
                                <div class="feature-card" id="progressCard" style="display: none;">
                                    <h5><i class="bi bi-activity"></i> 上传进度</h5>
                                    <div class="progress mb-3">
                                        <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%">
                                        </div>
                                    </div>
                                    <div id="progressText">准备上传...</div>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            成功: <span id="successCount">0</span> |
                                            失败: <span id="failCount">0</span> |
                                            总计: <span id="totalCount">0</span>
                                        </small>
                                    </div>
                                </div>

                                <!-- 上传结果 -->
                                <div class="feature-card" id="resultCard" style="display: none;">
                                    <h5><i class="bi bi-check-circle"></i> 上传完成</h5>
                                    <div id="uploadResults"></div>
                                    <div class="mt-3" id="downloadSection" style="display: none;">
                                        <button class="btn btn-success w-100" id="downloadExcel">
                                            <i class="bi bi-download"></i> 下载映射表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- 模板填充选项卡 -->
                    <div class="tab-pane fade" id="template-tab-pane" role="tabpanel" aria-labelledby="template-tab" tabindex="0">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="feature-card">
                                    <div class="text-center mb-4">
                                        <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);">
                                            <i class="bi bi-file-earmark-spreadsheet"></i>
                                        </div>
                                        <h4 class="mt-3">📄 模板填充</h4>
                                        <p class="text-muted">方法2的第二步：使用链接映射表自动填充亚马逊模板</p>
                                    </div>

                                    <div class="alert alert-info border-0" style="background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);">
                                        <h6><i class="bi bi-info-circle"></i> 操作流程</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><i class="bi bi-1-circle text-primary"></i> 上传亚马逊模板文件</p>
                                                <p class="mb-1"><i class="bi bi-2-circle text-primary"></i> 上传商品分类报告</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><i class="bi bi-3-circle text-primary"></i> 导入图片链接映射表</p>
                                                <p class="mb-1"><i class="bi bi-4-circle text-primary"></i> 自动填充并下载</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 更新方式选择 -->
                                    <div class="mb-4">
                                        <label class="form-label">
                                            <i class="bi bi-arrow-repeat text-primary"></i>
                                            更新方式 <span class="text-danger">*</span>
                                        </label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card h-100 border-info">
                                                    <div class="card-body p-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="updateMode" id="partialUpdate" value="partial">
                                                            <label class="form-check-label fw-bold" for="partialUpdate">
                                                                🔄 部分更新 (PartialUpdate)
                                                            </label>
                                                        </div>
                                                        <small class="text-muted">仅更新图片链接等部分信息</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card h-100 border-warning">
                                                    <div class="card-body p-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="updateMode" id="fullUpdate" value="full">
                                                            <label class="form-check-label fw-bold" for="fullUpdate">
                                                                🔄 全部更新 (Update)
                                                            </label>
                                                        </div>
                                                        <small class="text-muted">更新完整的商品信息</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 必需文件上传区域 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="templateFile" class="form-label">亚马逊模板文件 <span class="text-danger">*</span></label>
                                                <input type="file" class="form-control" id="templateFile" accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">选择要填充的亚马逊模板Excel文件</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="reportFile" class="form-label">
                                                    商品分类报告
                                                    <span class="text-danger" id="reportFileRequired">*</span>
                                                    <span class="text-muted" id="reportFileOptional" style="display: none;">(全部更新时必需)</span>
                                                </label>
                                                <input type="file" class="form-control" id="reportFile" accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">选择包含商品数据的分类报告文件</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 必需文件上传区域第二行 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="mappingFile" class="form-label">
                                                    <i class="bi bi-link-45deg text-success"></i> 图片链接映射文件 <span class="text-danger">*</span>
                                                </label>
                                                <input type="file" class="form-control" id="mappingFile" accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">
                                                    <i class="bi bi-info-circle text-primary"></i>
                                                    <strong>来源：</strong>图片链接管理功能生成的Excel文件<br>
                                                    <strong>内容：</strong>包含ASIN/MSKU与图片URL的对应关系
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="productInfoFile" class="form-label">
                                                    <i class="bi bi-box text-info"></i> 产品信息文件 (可选)
                                                </label>
                                                <input type="file" class="form-control" id="productInfoFile" accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">
                                                    <i class="bi bi-info-circle text-primary"></i>
                                                    如需更新包装尺寸、重量等信息，可勾选该选项并上传该文件
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 设置选项 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="marketSelect" class="form-label">目标市场</label>
                                                <select class="form-select" id="marketSelect">
                                                    <option value="US">美国 (US)</option>
                                                    <option value="UK">英国 (UK)</option>
                                                </select>
                                                <div class="form-text">选择目标销售市场，影响默认值设置</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check mt-4">
                                                    <input class="form-check-input" type="checkbox" id="useProductInfo" checked="">
                                                    <label class="form-check-label" for="useProductInfo">
                                                        使用产品信息文件数据
                                                    </label>
                                                    <div class="form-text">启用后将使用产品信息文件中的包装数据</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button class="btn btn-primary" id="fillTemplateBtn" disabled="">
                                            <i class="bi bi-gear"></i> 开始填充模板
                                        </button>
                                        <button class="btn btn-outline-secondary" id="clearTemplateBtn">
                                            <i class="bi bi-trash"></i> 清空选择
                                        </button>
                                    </div>
                                </div>

                                <!-- 报告测试功能 -->
                                <div class="feature-card mt-4">
                                    <h5><i class="bi bi-file-text"></i> 测试报告文件</h5>
                                    <p class="text-muted">上传商品报告文件，测试数据结构和内容</p>

                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="file" class="form-control" id="testReportFile" accept=".xlsx,.xls,.xlsm">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-outline-primary w-100" id="testReportBtn" disabled="">
                                                <i class="bi bi-search"></i> 测试报告
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 模板填充结果 -->
                                <div class="feature-card" id="templateResultCard" style="display: none;">
                                    <h5><i class="bi bi-check-circle"></i> 处理结果</h5>
                                    <div id="templateResults"></div>
                                    <div class="mt-3" id="templateDownloadSection" style="display: none;">
                                        <button class="btn btn-success w-100" id="downloadTemplateResult">
                                            <i class="bi bi-download"></i> 下载结果
                                        </button>
                                    </div>
                                </div>

                                <!-- 报告测试结果 -->
                                <div class="feature-card" id="reportTestCard" style="display: none;">
                                    <h5><i class="bi bi-clipboard-data"></i> 报告分析</h5>
                                    <div id="reportTestResults"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 历史记录选项卡 -->
                    <div class="tab-pane fade" id="history-tab-pane" role="tabpanel" aria-labelledby="history-tab" tabindex="0">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4><i class="bi bi-clock-history"></i> 历史映射表</h4>
                            <button class="btn btn-outline-primary" id="refreshHistory">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>

                        <div class="history-table">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>文件大小</th>
                                        <th>修改时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody"><tr>
            <td>url_mapping_20250731_225149.xlsm</td>
            <td>5.91 KB</td>
            <td>2025-07-31 22:51:49</td>
            <td>
                <a href="/api/download-history/url_mapping_20250731_225149.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250731_225124.xlsm</td>
            <td>5.91 KB</td>
            <td>2025-07-31 22:51:24</td>
            <td>
                <a href="/api/download-history/url_mapping_20250731_225124.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250731_224533.xlsm</td>
            <td>5.9 KB</td>
            <td>2025-07-31 22:45:33</td>
            <td>
                <a href="/api/download-history/url_mapping_20250731_224533.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250731_224519.xlsm</td>
            <td>5.82 KB</td>
            <td>2025-07-31 22:45:20</td>
            <td>
                <a href="/api/download-history/url_mapping_20250731_224519.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250712_151511.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-12 15:15:20</td>
            <td>
                <a href="/api/download-history/filled_template_20250712_151511.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250712_144647.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-12 14:46:58</td>
            <td>
                <a href="/api/download-history/filled_template_20250712_144647.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250712_142015.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-12 14:20:24</td>
            <td>
                <a href="/api/download-history/filled_template_20250712_142015.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250712_141702.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-12 14:17:12</td>
            <td>
                <a href="/api/download-history/filled_template_20250712_141702.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>模板填充结果_20250711_160215.xlsx</td>
            <td>5.15 KB</td>
            <td>2025-07-11 16:02:15</td>
            <td>
                <a href="/api/download-history/模板填充结果_20250711_160215.xlsx" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>模板填充结果_20250711_160214.xlsx</td>
            <td>5.15 KB</td>
            <td>2025-07-11 16:02:14</td>
            <td>
                <a href="/api/download-history/模板填充结果_20250711_160214.xlsx" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250711_134851.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-11 13:48:59</td>
            <td>
                <a href="/api/download-history/filled_template_20250711_134851.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250711_122342.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-11 12:24:09</td>
            <td>
                <a href="/api/download-history/filled_template_20250711_122342.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250711_115824.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-11 11:58:32</td>
            <td>
                <a href="/api/download-history/filled_template_20250711_115824.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250711_115525.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-11 11:55:35</td>
            <td>
                <a href="/api/download-history/filled_template_20250711_115525.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250711_113213.xlsm</td>
            <td>1.25 MB</td>
            <td>2025-07-11 11:32:23</td>
            <td>
                <a href="/api/download-history/filled_template_20250711_113213.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_084810.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 08:48:22</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_084810.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_084452.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 08:45:04</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_084452.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_083835.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 08:38:48</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_083835.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_081156.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 08:12:08</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_081156.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_080728.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 08:07:38</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_080728.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_080112.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 08:01:24</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_080112.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_075614.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 07:56:26</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_075614.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_075138.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 07:51:48</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_075138.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250620_074641.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-20 07:46:50</td>
            <td>
                <a href="/api/download-history/filled_template_20250620_074641.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_170657.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 17:07:05</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_170657.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_144034.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 14:40:42</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_144034.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_143112.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 14:31:19</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_143112.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_141101.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 14:11:09</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_141101.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_140311.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 14:03:19</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_140311.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_135427.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 13:54:35</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_135427.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_134736.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 13:47:44</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_134736.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_134056.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 13:41:03</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_134056.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_115107.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 11:51:15</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_115107.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_112247.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 11:22:54</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_112247.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250619_083405.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-19 08:34:12</td>
            <td>
                <a href="/api/download-history/filled_template_20250619_083405.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_224104.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 22:41:19</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_224104.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_220553.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 22:06:06</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_220553.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_215838.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 21:58:53</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_215838.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_215356.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 21:54:05</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_215356.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_214327.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 21:43:40</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_214327.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250618_213331.xlsm</td>
            <td>11.28 KB</td>
            <td>2025-06-18 21:33:32</td>
            <td>
                <a href="/api/download-history/url_mapping_20250618_213331.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_212643.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 21:26:56</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_212643.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_204223.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 20:42:33</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_204223.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_165239.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 16:52:47</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_165239.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_164451.xlsm</td>
            <td>1.26 MB</td>
            <td>2025-06-18 16:44:59</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_164451.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_162920.xlsm</td>
            <td>1.23 MB</td>
            <td>2025-06-18 16:29:27</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_162920.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_154327.xlsm</td>
            <td>1.23 MB</td>
            <td>2025-06-18 15:43:34</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_154327.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_154209.xlsm</td>
            <td>1.23 MB</td>
            <td>2025-06-18 15:42:17</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_154209.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_100221.xlsm</td>
            <td>1.23 MB</td>
            <td>2025-06-18 10:02:28</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_100221.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250618_095632.xlsm</td>
            <td>1.23 MB</td>
            <td>2025-06-18 09:56:40</td>
            <td>
                <a href="/api/download-history/filled_template_20250618_095632.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250618_095454.xlsm</td>
            <td>11.28 KB</td>
            <td>2025-06-18 09:54:55</td>
            <td>
                <a href="/api/download-history/url_mapping_20250618_095454.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>亚马逊图片模板_20250618_095318.xlsm</td>
            <td>11.27 KB</td>
            <td>2025-06-18 09:53:18</td>
            <td>
                <a href="/api/download-history/亚马逊图片模板_20250618_095318.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250617_162934.xlsm</td>
            <td>11.27 KB</td>
            <td>2025-06-17 16:29:35</td>
            <td>
                <a href="/api/download-history/url_mapping_20250617_162934.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>filled_template_20250617_162847.xlsm</td>
            <td>1.23 MB</td>
            <td>2025-06-17 16:28:54</td>
            <td>
                <a href="/api/download-history/filled_template_20250617_162847.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250617_161256.xlsm</td>
            <td>11.27 KB</td>
            <td>2025-06-17 16:12:57</td>
            <td>
                <a href="/api/download-history/url_mapping_20250617_161256.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr><tr>
            <td>url_mapping_20250617_155453.xlsm</td>
            <td>11.27 KB</td>
            <td>2025-06-17 15:54:53</td>
            <td>
                <a href="/api/download-history/url_mapping_20250617_155453.xlsm" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        </tr></tbody>
                            </table>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-3">处理中，请稍候...</div>
        </div>
    </div>

    <!-- jQuery库 -->
    <script src="/static/js/jquery.min.js" onerror="console.error('❌ 本地jQuery加载失败，尝试CDN版本'); this.src='https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js'"></script>
    
    <!-- Bootstrap JS with improved CDN fallback -->
    <script src="/static/js/bootstrap.bundle.min.js" onload="console.log('✅ 本地Bootstrap加载成功')" onerror="console.error('❌ 本地Bootstrap加载失败，尝试CDN版本'); loadBootstrapFromCDN()"></script>

    <!-- Bootstrap CDN备用方案 -->
    <script>
        /**
         * 🛡️ Bootstrap CDN备用加载函数
         * 当本地Bootstrap文件加载失败时自动使用CDN版本
         */
        function loadBootstrapFromCDN() {
            console.log('🔄 正在从CDN加载Bootstrap...');

            // 尝试多个CDN源
            const cdnSources = [
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js',
                'https://unpkg.com/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
            ];

            let currentIndex = 0;

            function tryLoadCDN() {
                if (currentIndex >= cdnSources.length) {
                    console.error('❌ 所有Bootstrap CDN源都加载失败，使用手动实现');
                    window.bootstrapFallbackMode = true;
                    initializeManualBootstrapFeatures();
                    return;
                }

                const script = document.createElement('script');
                script.src = cdnSources[currentIndex];
                script.onload = function() {
                    console.log(`✅ Bootstrap CDN版本加载成功 (源${currentIndex + 1})`);
                    // 重新初始化Bootstrap相关功能
                    setTimeout(() => {
                        initializeBootstrapFeatures();
                    }, 100);
                };
                script.onerror = function() {
                    console.warn(`⚠️ CDN源${currentIndex + 1}加载失败，尝试下一个...`);
                    currentIndex++;
                    tryLoadCDN();
                };
                document.head.appendChild(script);
            }

            tryLoadCDN();
        }

        /**
         * 🔧 初始化Bootstrap功能
         */
        function initializeBootstrapFeatures() {
            if (typeof bootstrap !== 'undefined') {
                console.log('✅ Bootstrap 全局对象可用');
                
                // 初始化Bootstrap工具提示
                try {
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                    console.log(`✅ 初始化了 ${tooltipList.length} 个工具提示`);
                } catch (error) {
                    console.error('⚠️ 工具提示初始化失败:', error);
                }
                
                // 初始化Bootstrap Tab功能
                initializeBootstrapTabs();
                
                // 初始化Bootstrap模态框
                initializeBootstrapModals();
                
                return true;
            }
            return false;
        }

        /**
         * 📋 初始化Bootstrap Tab功能
         */
        function initializeBootstrapTabs() {
            try {
                const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
                console.log(`🔍 找到 ${tabButtons.length} 个Tab按钮`);
                
                tabButtons.forEach((button, index) => {
                    try {
                        const tabInstance = new bootstrap.Tab(button);
                        console.log(`✅ Tab按钮 ${index + 1} 初始化成功:`, button.getAttribute('data-bs-target'));
                        
                        // 添加点击事件监听
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            console.log('[Tab调试] 点击Tab按钮:', this.getAttribute('data-bs-target'));
                            tabInstance.show();
                        });
                        
                    } catch (error) {
                        console.error(`❌ Tab按钮 ${index + 1} 初始化失败:`, error);
                        // 为失败的Tab添加手动处理
                        addManualTabHandler(button);
                    }
                });
                
            } catch (error) {
                console.error('❌ Bootstrap Tab整体初始化失败:', error);
                initializeManualTabs();
            }
        }

        /**
         * 📦 初始化Bootstrap模态框
         */
        function initializeBootstrapModals() {
            try {
                const modalTriggers = document.querySelectorAll('[data-bs-toggle="modal"]');
                console.log(`🔍 找到 ${modalTriggers.length} 个模态框触发器`);
                
                modalTriggers.forEach((trigger, index) => {
                    try {
                        trigger.addEventListener('click', function(e) {
                            e.preventDefault();
                            const targetId = this.getAttribute('data-bs-target');
                            const targetModal = document.querySelector(targetId);
                            if (targetModal && typeof bootstrap !== 'undefined') {
                                const modalInstance = new bootstrap.Modal(targetModal);
                                modalInstance.show();
                            }
                        });
                        
                    } catch (error) {
                        console.error(`❌ 模态框触发器 ${index + 1} 初始化失败:`, error);
                    }
                });
                
            } catch (error) {
                console.error('❌ Bootstrap模态框整体初始化失败:', error);
            }
        }

        /**
         * 🛠️ 手动实现Bootstrap功能（备用方案）
         */
        function initializeManualBootstrapFeatures() {
            console.log('🔧 启用手动Bootstrap功能实现');
            window.bootstrapFallbackMode = true;
            
            // 手动实现Tab功能
            initializeManualTabs();
            
            // 手动实现模态框功能
            initializeManualModals();
            
            // 手动实现下拉菜单功能
            initializeManualDropdowns();
        }

        /**
         * 📋 手动实现Tab切换功能
         */
        function initializeManualTabs() {
            console.log('🔧 启用手动Tab切换功能');
            
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                addManualTabHandler(button);
            });
        }

        /**
         * 🎯 为单个Tab按钮添加手动处理
         */
        function addManualTabHandler(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('[手动Tab] 点击Tab按钮:', this.getAttribute('data-bs-target'));
                
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);
                
                if (targetPane) {
                    // 隐藏所有tab内容
                    const allPanes = document.querySelectorAll('.tab-pane');
                    allPanes.forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });
                    
                    // 移除所有tab按钮的active状态
                    const allButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
                    allButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    
                    // 显示目标tab内容
                    targetPane.classList.add('show', 'active');
                    
                    // 激活当前tab按钮
                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');
                    
                    console.log('✅ 手动Tab切换完成');
                    
                    // 如果有amazonUploader实例，调用其Tab切换方法
                    if (window.amazonUploader && window.amazonUploader.manualTabSwitch) {
                        window.amazonUploader.manualTabSwitch(targetId);
                    }
                } else {
                    console.error('❌ 未找到目标Tab内容:', targetId);
                }
            });
        }

        /**
         * 📦 手动实现模态框功能
         */
        function initializeManualModals() {
            console.log('🔧 启用手动模态框功能');
            
            const modalTriggers = document.querySelectorAll('[data-bs-toggle="modal"]');
            modalTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-bs-target');
                    showManualModal(targetId);
                });
            });
            
            // 为关闭按钮添加事件
            document.addEventListener('click', function(e) {
                if (e.target.hasAttribute('data-bs-dismiss') && e.target.getAttribute('data-bs-dismiss') === 'modal') {
                    const modal = e.target.closest('.modal');
                    if (modal) {
                        hideManualModal(modal);
                    }
                }
            });
        }

        /**
         * 📦 显示手动模态框
         */
        function showManualModal(targetId) {
            const modal = document.querySelector(targetId);
            if (modal) {
                modal.style.display = 'block';
                modal.classList.add('show');
                document.body.classList.add('modal-open');
                
                // 添加背景遮罩
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.onclick = () => hideManualModal(modal);
                document.body.appendChild(backdrop);
                
                console.log('✅ 手动模态框显示成功');
            }
        }

        /**
         * 📦 隐藏手动模态框
         */
        function hideManualModal(modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
            
            // 移除背景遮罩
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            console.log('✅ 手动模态框隐藏成功');
        }

        /**
         * 📂 手动实现下拉菜单功能
         */
        function initializeManualDropdowns() {
            console.log('🔧 启用手动下拉菜单功能');
            
            const dropdownTriggers = document.querySelectorAll('[data-bs-toggle="dropdown"]');
            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const dropdown = this.nextElementSibling;
                    if (dropdown && dropdown.classList.contains('dropdown-menu')) {
                        const isShown = dropdown.classList.contains('show');
                        
                        // 关闭所有其他下拉菜单
                        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                            menu.classList.remove('show');
                        });
                        
                        // 切换当前下拉菜单
                        if (!isShown) {
                            dropdown.classList.add('show');
                        }
                    }
                });
            });
            
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function() {
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                });
            });
        }

        /**
         * 🎯 改进的错误处理模态框
         */
        function showDetailedError(title, message) {
            console.log('🚨 显示详细错误信息:', title);
            
            // 移除已存在的错误弹窗
            const existingModal = document.getElementById('errorModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 创建模态对话框显示详细错误信息
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'errorModal';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle-fill"></i> ${title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" onclick="hideManualModal(document.getElementById('errorModal'))"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-danger">
                                <pre style="white-space: pre-wrap; margin-bottom: 0; font-family: system-ui, -apple-system, sans-serif; font-size: 14px;">${message}</pre>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="hideManualModal(document.getElementById('errorModal'))">关闭</button>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到页面并显示
            document.body.appendChild(modal);
            
            // 根据Bootstrap是否可用选择显示方式
            if (typeof bootstrap !== 'undefined' && !window.bootstrapFallbackMode) {
                try {
                    const modalInstance = new bootstrap.Modal(modal);
                    modalInstance.show();
                    
                    // 监听关闭事件，清理DOM元素
                    modal.addEventListener('hidden.bs.modal', function () {
                        modal.remove();
                    });
                } catch (error) {
                    console.error('❌ Bootstrap模态框创建失败，使用手动方式:', error);
                    showManualModal('#errorModal');
                }
            } else {
                showManualModal('#errorModal');
            }
        }
    </script>

    <!-- 自定义JavaScript -->
    <script src="/static/js/app.js?v=20250712142000"></script>
    <script>
        // 改进的初始化函数
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 [HTML调试] DOMContentLoaded 事件触发');

            // 等待所有脚本加载完成后再进行初始化
            setTimeout(function() {
                console.log('🔍 开始Bootstrap兼容性检查...');

                // 检查Bootstrap是否可用
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap 全局对象可用');
                    initializeBootstrapFeatures();
                } else {
                    console.warn('⚠️ Bootstrap 全局对象不可用，启用备用方案');
                    console.log('🔄 将在3秒后重新检查Bootstrap状态...');

                    // 延迟重新检查，给CDN加载更多时间
                    setTimeout(function() {
                        if (typeof bootstrap !== 'undefined') {
                            console.log('✅ Bootstrap 延迟检查成功，重新初始化');
                            initializeBootstrapFeatures();
                        } else {
                            console.log('⚠️ Bootstrap 仍不可用，使用手动实现');
                            initializeManualBootstrapFeatures();
                        }
                    }, 3000);
                }

                // 确保只初始化一次应用实例
                if (!window.amazonUploader) {
                    console.log('🔧 [HTML调试] 首次初始化 AmazonImageUploader');
                    window.amazonUploader = new AmazonImageUploader();
                } else {
                    console.log('✅ [HTML调试] AmazonImageUploader 已存在，跳过初始化');
                }

                // 加载配置信息
                loadConfig();

                console.log('🎉 页面初始化完成');
            }, 100); // 延迟100ms确保所有脚本都已加载
        });

        // 🛠️ 工具函数库 - 兼容Bootstrap和手动实现
        
        /**
         * 📊 格式化文件大小显示
         */
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        /**
         * 🔄 智能加载状态显示
         */
        function showLoading(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                if (show) {
                    loadingOverlay.style.display = 'flex';
                } else {
                    loadingOverlay.style.display = 'none';
                }
            } else if (show) {
                // 创建加载遮罩
                const overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    color: white;
                    font-size: 18px;
                `;
                overlay.innerHTML = '<div>🔄 处理中，请稍候...</div>';
                document.body.appendChild(overlay);
            }
        }

        /**
         * ❌ 简单错误提示
         */
        function showError(message) {
            console.error('💥 错误:', message);

            // 尝试使用Toast通知，如果失败则使用alert
            try {
                showToast('错误', message, 'danger');
            } catch (error) {
                alert('错误: ' + message);
            }
        }

        /**
         * 📊 新的状态管理函数
         */
        function hideAllStatus() {
            const elements = ['processing-status', 'success-message', 'rename-error', 'rename-results-section'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.style.display = 'none';
            });
        }

        function showProcessing(message = '正在处理中...') {
            hideAllStatus();
            const statusDiv = document.getElementById('processing-status');
            const messageDiv = document.getElementById('processing-message');
            if (statusDiv) {
                if (messageDiv) messageDiv.textContent = message;
                statusDiv.style.display = 'block';
            }
        }

        function showSuccessStatus(message, details = '') {
            hideAllStatus();
            const successDiv = document.getElementById('success-message');
            const detailsDiv = document.getElementById('success-details');
            if (successDiv) {
                if (detailsDiv) detailsDiv.innerHTML = `<strong>${message}</strong><br>${details}`;
                successDiv.style.display = 'block';
            }
        }

        function showErrorStatus(message, details = '') {
            hideAllStatus();
            const errorDiv = document.getElementById('rename-error');
            const detailsDiv = document.getElementById('error-details');
            if (errorDiv) {
                if (detailsDiv) detailsDiv.innerHTML = `<strong>${message}</strong><br>${details}`;
                errorDiv.style.display = 'block';
            }
        }

        /**
         * ✅ 简单成功提示
         */
        function showSuccess(message) {
            console.log('🎉 成功:', message);
            
            // 尝试使用Toast通知，如果失败则使用alert
            try {
                showToast('成功', message, 'success');
            } catch (error) {
                alert('成功: ' + message);
            }
        }

        /**
         * 🍞 智能Toast通知 - 兼容Bootstrap和手动实现
         */
        function showToast(title, message, type = 'success') {
            console.log(`📢 显示Toast通知: ${title} - ${message}`);
            
            // 创建toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '1055';
                document.body.appendChild(toastContainer);
            }
            
            // 创建toast元素
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            <strong>${title}</strong><br>${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" onclick="this.parentElement.parentElement.remove()"></button>
                    </div>
                </div>
            `;
            
            const toastElement = document.createElement('div');
            toastElement.innerHTML = toastHtml;
            const toast = toastElement.firstElementChild;
            toastContainer.appendChild(toast);
            
            // 根据Bootstrap是否可用选择显示方式
            if (typeof bootstrap !== 'undefined' && !window.bootstrapFallbackMode) {
                try {
                    const toastInstance = new bootstrap.Toast(toast, {
                        autohide: true,
                        delay: 5000
                    });
                    toastInstance.show();
                    
                    // 监听隐藏事件，清理DOM
                    toast.addEventListener('hidden.bs.toast', function () {
                        toast.remove();
                    });
                } catch (error) {
                    console.error('❌ Bootstrap Toast创建失败，使用手动方式:', error);
                    showManualToast(toast);
                }
            } else {
                showManualToast(toast);
            }
        }

        /**
         * 🍞 手动Toast显示
         */
        function showManualToast(toast) {
            // 显示toast
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            toast.style.transition = 'all 0.3s ease';
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }

        // 重命名提交处理函数 - 完整的原生JavaScript实现
        function handleRenameSubmit(event) {
            console.log('🔧 重命名提交处理开始');
            event.preventDefault();
            event.stopPropagation();
            
            // 验证表单
            const excelFile = document.getElementById('rename_excel_file').files[0];
            const imageFiles = document.getElementById('rename_image_folder').files;
            const mainChecked = document.getElementById('process_main').checked;
            const sceneChecked = document.getElementById('process_scene').checked;
            const swatchChecked = document.getElementById('process_swatch').checked;
            
            // 验证文件
            if (!excelFile) {
                document.getElementById('rename_excel_file').classList.add('is-invalid');
                return false;
            }
            
            if (imageFiles.length === 0) {
                document.getElementById('rename_image_folder').classList.add('is-invalid');
                return false;
            }
            
            // 验证至少选择了一个处理选项
            if (!mainChecked && !sceneChecked && !swatchChecked) {
                showErrorStatus('请至少选择一个处理选项', '请勾选主图、场景图或色块图中的至少一项');
                return false;
            }

            console.log('✅ 表单验证通过，开始提交');

            // 显示处理状态
            showProcessing('正在上传文件和处理图片重命名，请稍候...');
            
            // 准备表单数据
            const formData = new FormData();
            formData.append('excel_file', excelFile);
            
                         // 添加所有图片文件
             for (let i = 0; i < imageFiles.length; i++) {
                 formData.append('image_folder', imageFiles[i], imageFiles[i].webkitRelativePath || imageFiles[i].name);
             }
            
            // 添加处理选项
            if (mainChecked) formData.append('process_main', 'on');
            if (sceneChecked) formData.append('process_scene', 'on');
            if (swatchChecked) formData.append('process_swatch', 'on');
            if (document.getElementById('scene_generic').checked) {
                formData.append('scene_generic', 'on');
            }
            
            console.log('📤 发送重命名请求到服务器');
            
            // 发送请求
            fetch('/api/rename-images', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('📥 收到服务器响应，状态:', response.status);
                
                if (response.ok) {
                    // 成功响应，处理文件下载
                    return response.blob();
                } else {
                    // 错误响应，解析错误信息
                    return response.text().then(text => {
                        try {
                            const errorData = JSON.parse(text);
                            throw new Error(errorData.error || '处理失败');
                        } catch (parseError) {
                            // 如果JSON解析失败，直接使用服务器返回的文本作为错误信息
                            console.log('JSON解析失败，使用原始响应文本:', text);
                            throw new Error(text || '处理失败，请检查文件和选项');
                        }
                    });
                }
            })
            .then(blob => {
                console.log('✅ 文件处理成功，开始下载');

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'renamed_images_' + new Date().getTime() + '.zip';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // 显示成功提示
                showSuccessStatus('图片重命名处理完成！',
                    `✅ 已自动下载ZIP文件<br>
                     📁 文件名: renamed_images_${new Date().getTime()}.zip<br>
                     💡 请检查浏览器下载目录`);
            })
            .catch(error => {
                console.error('❌ 处理失败:', error);

                // 显示错误信息
                showErrorStatus('图片重命名处理失败',
                    `❌ 错误详情: ${error.message || '未知错误'}<br>
                     💡 请检查文件格式和目录结构是否正确`);
             });
         }
         
         // 颜色匹配测试函数
         function handleColorMatchTest(event) {
             console.log('🎨 开始颜色匹配测试');
             event.preventDefault();
             event.stopPropagation();
             
             // 验证表单
             const excelFile = document.getElementById('rename_excel_file').files[0];
             const imageFiles = document.getElementById('rename_image_folder').files;
             
             // 验证文件
             if (!excelFile) {
                 alert('请先选择Excel文件');
                 return false;
             }
             
             if (imageFiles.length === 0) {
                 alert('请先选择图片文件夹');
                 return false;
             }
             
             console.log('✅ 颜色匹配测试验证通过，开始提交');
             
             // 显示加载状态
             showLoading(true);
             
             // 准备表单数据
             const formData = new FormData();
             formData.append('excel_file', excelFile);
             
             // 添加所有图片文件
             for (let i = 0; i < imageFiles.length; i++) {
                 formData.append('image_folder', imageFiles[i], imageFiles[i].webkitRelativePath || imageFiles[i].name);
             }
             
             console.log('📤 发送颜色匹配测试请求到服务器');
             
             // 发送请求
             fetch('/api/test-color-matching', {
                 method: 'POST',
                 body: formData
             })
             .then(response => {
                 console.log('📥 收到颜色匹配测试响应，状态:', response.status);
                 return response.json();
             })
             .then(data => {
                 console.log('✅ 颜色匹配测试成功', data);
                 showLoading(false);
                 
                 // 显示测试结果
                 alert('颜色匹配测试完成！\n结果：' + JSON.stringify(data, null, 2));
             })
             .catch(error => {
                 console.error('❌ 颜色匹配测试失败:', error);
                 showLoading(false);
                 
                 // 显示错误信息
                 alert('颜色匹配测试失败：' + (error.message || '未知错误'));
             });
         }

        // 加载配置信息
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const result = await response.json();

                if (result.success) {
                    // 添加DOM元素存在性检查，避免null错误
                    const apiUrlElement = document.getElementById('apiUrl');
                    const apiKeyElement = document.getElementById('apiKey');

                    if (apiUrlElement) {
                        apiUrlElement.textContent = result.config.api_url;
                    } else {
                        console.log('💡 apiUrl元素不存在，跳过配置显示');
                    }

                    if (apiKeyElement) {
                        apiKeyElement.textContent = result.config.api_key;
                    } else {
                        console.log('💡 apiKey元素不存在，跳过配置显示');
                    }

                    console.log('✅ 配置加载成功');
                } else {
                    console.warn('⚠️ 配置加载失败:', result.message || '未知错误');
                }
            } catch (error) {
                console.error('❌ 加载配置失败:', error);
            }
        }

        // 显示图片重命名工具（方法1）
        function showRenameTools() {
            console.log('🔧 显示图片重命名工具（方法1）');

            // 完全隐藏方法选择区域
            const methodSelectionArea = document.querySelector('.row.g-4.mb-4');
            const methodIntroArea = document.querySelector('.row.mb-4');
            if (methodSelectionArea) methodSelectionArea.style.display = 'none';
            if (methodIntroArea) methodIntroArea.style.display = 'none';

            // 显示工具容器
            document.getElementById('toolsContainer').style.display = 'block';

            // 隐藏所有标签页
            document.querySelectorAll('.nav-item').forEach(item => {
                item.style.display = 'none';
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // 只显示方法1相关的标签页
            const method1Tabs = ['rename-tab', 'history-tab']; // 图片重命名工具 + 历史记录
            method1Tabs.forEach(tabId => {
                const tabElement = document.getElementById(tabId);
                if (tabElement) {
                    tabElement.parentElement.style.display = 'block';
                }
            });

            // 激活重命名标签
            const renameTab = document.getElementById('rename-tab');
            const renamePane = document.getElementById('rename-tab-pane');

            // 清除所有活跃状态
            document.querySelectorAll('.nav-link').forEach(tab => {
                tab.classList.remove('active');
                tab.setAttribute('aria-selected', 'false');
            });

            // 激活重命名标签
            renameTab.classList.add('active');
            renameTab.setAttribute('aria-selected', 'true');
            renamePane.classList.add('show', 'active');

            // 滚动到页面顶部
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 显示模板工具（方法2）
        function showTemplateTools() {
            console.log('📋 显示模板工具（方法2）');

            // 完全隐藏方法选择区域
            const methodSelectionArea = document.querySelector('.row.g-4.mb-4');
            const methodIntroArea = document.querySelector('.row.mb-4');
            if (methodSelectionArea) methodSelectionArea.style.display = 'none';
            if (methodIntroArea) methodIntroArea.style.display = 'none';

            // 显示工具容器
            document.getElementById('toolsContainer').style.display = 'block';

            // 隐藏所有标签页
            document.querySelectorAll('.nav-item').forEach(item => {
                item.style.display = 'none';
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // 只显示方法2相关的标签页
            const method2Tabs = ['upload-tab', 'template-tab', 'history-tab']; // 图片链接管理 + 模板填充 + 历史记录
            method2Tabs.forEach(tabId => {
                const tabElement = document.getElementById(tabId);
                if (tabElement) {
                    tabElement.parentElement.style.display = 'block';
                }
            });

            // 激活图片链接管理标签
            const uploadTab = document.getElementById('upload-tab');
            const uploadPane = document.getElementById('upload-tab-pane');

            // 清除所有活跃状态
            document.querySelectorAll('.nav-link').forEach(tab => {
                tab.classList.remove('active');
                tab.setAttribute('aria-selected', 'false');
            });

            // 激活图片链接管理标签
            uploadTab.classList.add('active');
            uploadTab.setAttribute('aria-selected', 'true');
            uploadPane.classList.add('show', 'active');

            // 滚动到页面顶部
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 返回方法选择
        function backToMethodSelection() {
            console.log('🔙 返回方法选择');

            // 隐藏工具容器
            document.getElementById('toolsContainer').style.display = 'none';

            // 重新显示方法选择区域
            const methodSelectionArea = document.querySelector('.row.g-4.mb-4');
            const methodIntroArea = document.querySelector('.row.mb-4');
            if (methodSelectionArea) methodSelectionArea.style.display = 'flex';
            if (methodIntroArea) methodIntroArea.style.display = 'flex';

            // 显示所有标签页（恢复默认状态）
            document.querySelectorAll('.nav-item').forEach(item => {
                item.style.display = 'block';
            });

            // 清除所有活跃状态
            document.querySelectorAll('.nav-link').forEach(tab => {
                tab.classList.remove('active');
                tab.setAttribute('aria-selected', 'false');
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // 滚动回顶部的方法选择区域
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 清除图片重命名工具的Excel文件选择
        function clearRenameExcelFile() {
            const fileInput = document.getElementById('rename_excel_file');
            const filenameDisplay = document.getElementById('rename_excel_filename_display');

            if (fileInput) {
                fileInput.value = '';
                fileInput.classList.remove('is-invalid');
            }

            if (filenameDisplay) {
                filenameDisplay.style.display = 'none';
            }

            console.log('🗑️ 已清除图片重命名工具的Excel文件选择');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            // 注释掉重复的模板填充功能设置，由app.js处理
            // setupTemplateFeatures();
        });

        // 模板填充功能由 app.js 中的 AmazonImageUploader 类处理
        // 以下代码已移至 app.js，避免重复事件绑定
    </script>


</div></body></html>
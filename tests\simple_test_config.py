#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试配置文件
仅包含必要的配置信息，方便后续程序调用
"""

# 测试配置信息
TEST_CONFIG = {
    # Excel测试文件配置
    "excel_file": {
        "path": r"D:\临时文件\TEST\图片上传工具\低价商城listing_grosgrain_ribbon.xlsx",
        "description": "低价商城罗纹带商品清单Excel文件"
    },
    
    # 图片源路径配置
    "image_source": {
        "path": r"\\*************\家庭共享\产品图片视频\白板\KRYVUS\罗纹带(普通)",
        "description": "KRYVUS罗纹带(普通)产品图片网络共享目录"
    },
    
    # 导出路径配置
    "export": {
        "base_path": r"D:\临时文件\TEST\图片上传工具",
        "description": "测试工具导出结果目录"
    }
}

def get_excel_path():
    """获取Excel文件路径"""
    return TEST_CONFIG["excel_file"]["path"]

def get_image_source_path():
    """获取图片源路径"""
    return TEST_CONFIG["image_source"]["path"]

def get_export_path():
    """获取导出路径"""
    return TEST_CONFIG["export"]["base_path"]

def get_all_config():
    """获取完整配置信息"""
    return TEST_CONFIG

if __name__ == "__main__":
    """显示配置信息"""
    print("📋 测试配置信息")
    print("=" * 40)
    print(f"Excel文件: {get_excel_path()}")
    print(f"图片源路径: {get_image_source_path()}")
    print(f"导出路径: {get_export_path()}")
    print("✅ 配置加载完成")